# Titel

**User Story**
Als <persona> wil ik .... , zodat ik ....

De User Story beschrijft de focus. Deze is vanuit de behoefte van een gebruiker opgesteld, volgens de richtlijnen van een user story.


## Beschrijving

In de beschrijving geef je details over de user story en schets je de context.
Verwijzing naar documentatie of diagrammen is aanbevolen.


## Benodigde kennis & vaardigheden

De benodigde kennis bijvoorbeeld te vinden in de Learning Story's in de LJ-Git, of in de Knowledge base.

- [ ] 

## Requirements

```Beschrijving van essentiële onderdelen.```
De `product` bevat de volgende onderdelen:

- [ ] ...
- [ ] ...
- [ ] ...

## Acceptatie criteria

Acceptatie criteria zijn specifieke eisen aan waaraan de user story moet voldoen. Deze zijn meestal uniek per user story.
- [ ] ... 
- [ ] ... 
- [ ] ...

## Definitions of Done

- [ ] Alle acceptatie criteria zijn behaald.
- [ ] Het werk is gedocumenteerd, zodat een teamlid hiermee verder kan.
- [ ] Het werk is geschreven in standaard Nederlands of Engels.
- [ ] Het werk staat op de gitlab repository.
- [ ] Het werk is gereviewd door een peer (mede student).

Software specifiek:
- [ ] De deliverables zijn beschikbaar in de Gitlab.
- [ ] De code is opgesteld volgens de HBO-ICT code conventions.
- [ ] De code is handmatig functioneel getest op fouten.
- [ ] De code werkt zonder fouten bij normaal gebruik.

## Deliverables voor Portfolio

- [ ] Markdown 
- [ ] HTML /CSS 
- [ ] Python / Flask
- [ ] Docker