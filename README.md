# CS Semester 2: Secure Infrastructure

<img src="hbo-ict-logo.png" width="175" height="175" alt="HBO-ICT-LOGO">

## Inleiding
Welkom bij het project voor Cyber Security in semester 2 (2025 - 2026)! Deze repository is jouw centrale plek om alles wat je nodig hebt voor dit project te vinden. Van documentatie tot user stories, alles is hier gestructureerd. Neem de tijd om deze README goed door te lezen, zodat je precies weet hoe je dit project succesvol kunt aanpakken.

Wat vind je hier?
- **[Opdrachtomschrijving](docs/getting-started/opdrachtomschrijving.md)**: <PERSON><PERSON> wat je dit blok gaat maken en aan welke eisen je project moet voldoen.
- **[Projectbrief](docs/getting-started/project_brief.md)**: Beschrijving van het project en de technische eisen vanuit de fictieve opdrachtgever.
- **[User Story Template](.gitlab/issue_templates/user_story_template.md)**: Gebruik dit format om je user stories goed op te stellen.
- **[Learning Story Template](.gitlab/issue_templates/learning_story_template.md)**: Leg hier je leerdoelen vast en hoe je deze gaat bereiken.
- **[Docs-map](https://gitlab.fdmci.hva.nl/propedeuse-hbo-ict/onderwijs/start-projecten/2025-2026/cyber-security/secure-infrastructure-spiegelklas/-/tree/main/docs?ref_type=heads)**: Hier vind je alle technische documentatie en ondersteunende bestanden.

---

## Boards
In GitLab vind je onder **Issues > Boards** (te bereiken via de linker balk 👈🏽) verschillende handige boards om je werk te organiseren. Zo werken jij en je team overzichtelijk samen en houden jullie de voortgang bij. De volgende boards staan klaar:

- **Product backlog**: Verzamel hier alle user stories voor het project. Verdeel deze vervolgens over de sprints.
- **Sprint backlogs**:
  - Sprint 1 backlog: 1 september 2025 t/m 19 september 2025 (3 weken).
  - Sprint 2 backlog: 20 september 2025 t/m 12 oktober 2025 (ong. 3 weken).
  - Sprint 3 backlog: 13 oktober 2025 t/m 3 november 2025 (ong. 3 weken).
  - Sprint 4 backlog: 4 november 2025 t/m 24 november 2025 (3 weken).
  - Sprint 5 backlog: 25 november 2025 t/m 5 januari 2025 (ruim 3 weken).
  
  Daarna Medium Stake en start Blok 4 met sprint 6-10

### Hoe gebruik je de boards?
- Begin bij de **Product Backlog**: verzamel hier alle user stories die bij het project horen.
- Verdeel de user stories over de sprints door ze naar de juiste **Sprint Backlog** te slepen. **Je moet zelf de sprints aanmaken in GitLab en deze koppelen aan de juiste user stories.**
- Gebruik de volgende statussen om de voortgang bij te houden:
  - **To Do**: Taken die nog moeten worden opgepakt.
  - **Doing**: Taken waaraan je op dit moment werkt.
  - **Verify**: Taken die zijn afgerond maar nog moeten worden gecontroleerd door teamleden.
  - **Closed**: Taken die volledig zijn afgerond, getest en geaccepteerd.

---

## User stories
Een belangrijk onderdeel van dit project is het maken van je eigen user stories. Samen met je team bepaal je wat er nodig is om aan de eisen van het project te voldoen. Gebruik hierbij het volgende format:
- *"Als [type gebruiker] wil ik [actie] zodat [doel]."*

Koppel deze stories aan een sprint via de boards en werk ze uit tot heldere taken. Test je werk grondig voordat je een story op "Closed" zet!

> Gebruik de **[User Story Template](.gitlab/issue_templates/user_story_template.md)** om een consistent format te behouden.

---

## Learning stories
Naast user stories werk je ook met learning stories. Dit zijn documenten waarin je je persoonlijke leerdoelen vastlegt en beschrijft hoe je deze doelen gaat behalen. Het format voor learning stories bevat:

1. **Inleiding**: Wat wil je leren en waarom is dit belangrijk?
2. **Wat moet ik leren?**: Beschrijf concreet welke kennis of vaardigheden je wilt ontwikkelen.
3. **Hoe leer ik dit?**: Geef aan hoe je dit gaat aanpakken, zoals literatuurstudie, oefeningen of samenwerking met je team.

> Gebruik de **[Learning Story Template](.gitlab/issue_templates/learning_story_template.md)** om je leerproces vast te leggen.

---

## Technische documentatie 📄
In de map **docs** bewaar je alle technische documentatie. Denk hierbij aan:
- Diagrammen van de infrastructuur.
- Beschrijvingen van beveiligingsmaatregelen.
- Testplannen en resultaten.
- Code-documentatie zodat je teamgenoten begrijpen wat je hebt gebouwd.
- **Etc**: Denk aan alles wat relevant is voor het project.

**Belangrijk:**
- Alle documentatie moet worden opgeslagen in GitLab en gekoppeld zijn aan je broncode. Dit betekent dat je *geen* Word-documenten, txt-bestanden of andere losse bestanden buiten GitLab gebruikt.
- **Comments in je code tellen niet als documentatie**. Zorg ervoor dat je aparte, gestructureerde documentatie maakt onder de broncode.

Waarom is dit belangrijk?
- **Centralisatie**: Alle teamleden hebben toegang tot dezelfde documenten.
- **Versiebeheer**: Je kunt wijzigingen bijhouden en herstellen.
- **Professioneel werken**: Dit is een standaard werkwijze in het werkveld.

---

## Weights: Wat zijn dat?
**Weights** zijn een manier om het werk dat nodig is voor een user story te schatten en prioriteren. Door gebruik te maken van **Scrum Poker** kun je samen met je team inschatten hoeveel inspanning een taak kost. Dit helpt bij het plannen van je sprints en het verdelen van werk.

### Scrum Poker in het kort:
- Elk teamlid geeft een "gewicht" (bijvoorbeeld een getal tussen 1 en 13) aan een user story, gebaseerd op de complexiteit en tijdsinvestering.
- Vervolgens bespreekt het team de schattingen. Bij grote verschillen in de gewichten (bijvoorbeeld iemand kiest 2 en iemand anders 13), wordt er uitleg gegeven waarom.
- Uiteindelijk stemt het team over een gezamenlijk gewicht.

> Meer weten of direct Scrum Poker spelen? Ga naar [Scrum Poker Online](https://www.scrumpoker-online.org/en/).


### Hoe gebruik je weights in GitLab?
- Ga naar de eigenschappen van een user story en vul het gewicht (weight) in. 
- Gebruik lage waarden (bijvoorbeeld 1-3) voor eenvoudige taken en hogere waarden (bijvoorbeeld 8-13) voor complexe taken.
- Door deze gewichten te gebruiken, kun je beter inschatten hoeveel werk je team in een sprint kan oppakken.

---

## Planning
Je werkt in **sprints**, net als in een professionele omgeving. In totaal werk je aan 6 sprints:

1. **Sprint 1**: 3 februari 2025 t/m 14 februari 2025 (2 weken).
2. **Sprint 2**: 24 februari 2025 t/m 14 maart 2025 (3 weken).
3. **Sprint 3**: 17 maart 2025 t/m 4 april 2025 (3 weken).
4. **Sprint 4**: 7 april 2025 t/m 25 april 2025 (3 weken).
5. **Sprint 5**: 5 mei 2025 t/m 23 mei 2025 (3 weken).
6. **Sprint 6**: 26 mei 2025 t/m 13 juni 2025 (2 weken).

Hoe werkt het?
- Selecteer tijdens de **sprintplanning** de user stories die je denkt af te kunnen ronden in de sprint.
- Wijs de user stories toe aan een **Milestone** (je kunt dit doen via de eigenschappen van de story of via het Product Backlog board).
- Aan het einde van elke sprint moet er een werkend en getest product zijn. Stories die niet af zijn, verplaats je naar de volgende sprint.


