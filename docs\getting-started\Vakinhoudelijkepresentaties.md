# Vakinhoudelijke Presentaties: Secure Infrastructure

## Inleiding
In dit semester ga je je verder verdiepen in jouw werkveld. <PERSON><PERSON> van de manieren waarop je dit doet, is door middel van vakinhoudelijke presentaties. 
Je onderzoekt een relevant onderwerp en presenteert je bevindingen aan je klasgenoten. 
Dit helpt niet alleen om je kennis te verdiepen, maar ook om je presentatie- en onderzoeksvaardigheden te ontwikkelen.  

Deze presentaties vervangen traditionele hoorcolleges en workshops en bieden jou een actieve manier om de leerstof eigen te maken.\
Je leert om kritisch te zoeken naar informatie, deze te analyseren en over te brengen aan anderen.

In de eerste sprints staan fundamenten zoals **de CIA triad** en **de OWASP top 10** centraal.\
Later in het semester behandelen we complexere onderwerpen, zoals **NIS2, AVG, encryptiemethoden** en **cybersecurity-aanvallen**.

---

## Opdracht
Je presenteert samen met je team ieder een onderdeel van een of meer cybersecurity-onderwerpen.\
Je zorgt ervoor dat klasgenoten in **10 minuten** de essentie begrijpen en kunnen toepassen in hun project.

Met deze presentaties toon je bewijs aan voor verschillende leeruitkomsten, zoals:\
**Infrastructuur** - Kennis en een diepgaand begrip over relevante onderwerpen binnen je studie.\
**Onderzoekend Probleemoplossen** – het kritisch analyseren en verwerken van informatie.\
**Doelgericht Interacteren** – het helder en effectief communiceren van complexe onderwerpen.

Elke presentatie wordt voorbereid door het heel het team. Ieder doet aantoonbaar:\
*-* een deel van achtergrondonderzoek gedaan en verzameld relevante bronnen.\
*-* maakt een deel van presentatie gemaakt en gepresenteerd zijn eigen onderdeel.

### Structuur van de presentatie
Elke presentatie duurt ongeveer **10 minuten**, gevolgd door **5 minuten** voor vragen en feedback.\
Zorg ervoor dat je niet alleen deze punten behandelt, maar ook een samenhangend verhaal vertelt dat de kern van het onderwerp goed uitlegt. Gebruik praktijkvoorbeelden, demonstraties of vergelijkingen om je publiek te betrekken en de relevantie van het onderwerp duidelijk te maken.\
✅ Wat is het onderwerp en waarom is het relevant?  
✅ Wat zijn de belangrijkste (technische) inzichten?  
✅ Hoe kan deze kennis in de praktijk worden toegepast?  
✅ Hoe past het binnen de bredere context van Cyber Security?  

---

## Planning en Proactieve Aanpak
Elke sprint wordt vooraf bepaald welk onderwerp je in de volgende sprint presenteert:\
**- Sprint 1:** Kies je als team een onderwerp je in sprint 2 presenteert.\
**- Sprint 3:** Kies je als team een onderwerp je in sprint 3 presenteert, enzovoorts.

Maak gebruikt van de beschikbare lijst met onderwerpen en bespreek dit met de docent, zodat elk team een ander onderwerp behandeld.

### Go/No-Go-overleg 🚦

Minimaal **één week vóór** de presentatie vindt een Go/No-Go moment plaats. Tijdens dit overleg wordt besproken:\
*-* Een **bronnenlijst**\
*-* Een samenvatting van de **belangrijkste inzichten**

Een docent controleert de betrouwbaarheid en kwaliteit van de informatie. Zonder een **Go** gaat de presentatie niet door.\
Vraag dus eerder feedback, zodat je nog wijzigingen kunt doorvoeren.

- Bij een no-go moet je je presentatie aanpassen en opnieuw inleveren.

---

## Ondersteunend Materiaal
Bij elke presentatie lever je de volgende onderdelen in:
- **Presentatie van minimaal 2 slides per student**, dus exclusief voorblad.
- **Bronnenlijst** per slide en aan het eind, waarin je verwijst naar betrouwbare bronnen.

📌 *Gebruik de richtlijnen uit de Knowledgebase:*
- [Bronnen zoeken](https://knowledgebase.hbo-ict-hva.nl/2_professional_skills/onderzoekend_probleemoplossen/onderzoeken/bronnenzoeken/)
- [Bronvermelding](https://knowledgebase.hbo-ict-hva.nl/2_professional_skills/onderzoekend_probleemoplossen/onderzoeken/bronvermelding/)

Het ondersteunend materiaal kan op **DLO** worden geplaatst voor latere raadpleging.

---

## Onderwerpenlijst
De onderwerpen waarover je kunt presenteren, vind je in de [onderwerpenlijst](docs/getting-started/onderwerpen.md).  
Vanaf sprint 4 mag de klas, in overleg met de docent, hiervan afwijken.

---

## Beoordeling
Je presentatie wordt beoordeeld op vier onderdelen:

**Inhoud**\
*-* Is het onderwerp voldoende diepgaand onderzocht en relevant?\
*-* Is er een duidelijk link met het vakgebied? 

**Structuur en opbouw**\
*-* Is de presentatie logisch opgebouwd en goed gestructureerd?\
*-* Is de presentatie gestructureerd, begrijpelijk en visueel aantrekkelijk?

**Presentatievaardigheden**\
*-* Wordt er duidelijk en boeiend gepresenteerd?\
*-* Worden vragen en feedback goed behandeld? 

**Brongebruik**\
*-* Worden betrouwbare en relevante bronnen correct gebruikt en vermeld?

---

## Feedback en Reflectie
- Een klasgenoot verzamelt feedback tijdens jouw presentatie.
- Gebruik deze feedback om je leerproces vast te leggen in je portfolio.

---

## Tips voor een succesvolle presentatie
- Bereid je goed voor: Oefen je presentatie meerdere keren en zorg dat je de tijd goed benut.
- Gebruik Visuals: Grafieken, afbeeldingen en diagrammen helpen om complexe informatie duidelijk te maken.
- Maak het interactief: Stel vragen aan je publiek en betrek hen bij je verhaal.
- Wees kritisch op je bronnen: Zorg dat je informatie betrouwbaar en actueel is.

🎯 **Succes met je presentaties!**
