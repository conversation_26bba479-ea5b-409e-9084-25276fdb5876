# Product requirements

To win this bid, the ALSSM Corporation had to agree to several requirements for this project. These strict guidelines define which technologies should be used within the project. Deviation from these guidelines is only allowed with documented permission of the client, and a complete justification for deviating from the guidelines. Failure to adhere to the prescribed technology stack without prior permission will lead to an automatic rejection of the proof of concept.

The following requirements are set for the product:

## Software

These requirements pertain to the software application, and any applications that exist to support the application.

### General:

- Security controls, such as (but not limited to) authentication, authorization, and encryption, have been used to lower the risk profile of the various applications.

### Application:

- The code is structured logically by creating and applying functions, classes, and modules where its usage is most relevant.
- The application takes potential errors into account (avoids happy-path coding).
- The application logs relevant errors for use in (for example) a SOC/SIEM setting.
- The application has been documented, including:
  - Self-documenting code;
  - An architecture description;
  - Code documentation for developers;
  - The application is built on an existing web application framework, such as Flask.
  - External modules should only be used if (and only if!) the security of the module has been assessed.

### Database:

- The database is normalized to the third form.
- The database uses integrity constraints.
- The database design and underlying logic has been documented, including:
  - Entity Relationship Diagram.
  - Extended Entity Relationship Diagram.
  - Database roles and their permissions.

## Infrastructure

These requirements pertain to the infrastructure.

### Infrastructure:

- The infrastructure uses IPv4 addressing.
- The application can be approached from inside and outside the company network, keeping in mind relevant security considerations.
- The infrastructure is built in various stages, using the DTAP framework.
- The infrastructure for the Development and Testing phases is built on a Docker framework.
- All infrastructure components in Development and Testing are built using Docker images.
- The infrastructure is segmented according to secure design principles.
- Network security techniques, such as firewall, IDS, DMZ and reverse proxie are used to protect the infrastructure and its components.
- All rules in the firewall and IDS are written by hand, and can be directly linked to security requirements or identified threats.
- Relevant network traffic is encrypted using appropriate encryption protocols.
- The infrastructure has been tested to assess functionality and security.
- The infrastructure has been documented using the provided template "infrastructure design".