# Project Brief

## Introduction

Congratulations! Your company, ALSSM[^1] has won the bid to develop a new web application for a company operating in the Netherlands. The client wishes to automate (part of) one of their primary processes with a custom-built application. After the successes of the Secret Manager and the Data Dashboard, the company has faith in your abilities to develop this new application. They have given you free reign in choosing the exact focus of the project: as long as you can argue why your application will improve productivity for a primary process, the organisation will provide funding.

To identify which process should be automated, the client has requested a brief business analysis during the first sprint. They would like you to identify and describe the primary processes of the organisation. To understand how this primary process is executed, the business has asked you to list the various stakeholders of the primary process, and their role in the process. Using a simplified user research, the business has requested that you identify potential areas of improvement for the primary process, and pitch an application that can provide that improvement. At the end of the first sprint there is a go/no go presentation for the application.   

## Current state of the project

Currently, little work has been done on the application. No infrastructure has been built, nor is there an existing framework for the application. It is, as they say, a greenfields project. However, a brief list of functional and technical requirements have been provided by the client. This list is not exhaustive - you are explicitly requested to do further research and add to the existing requirements. You can find the full list in the [Product Requirements](docs/getting-started/product_requirements.md).

## Deliverables

At the end of the project, the client has requested four major deliverables. These deliverables _must_ be completed by the end of the semester. 

### User Research:

An in-depth user research that analyses the needs and use cases of the various types of users and stakeholders of the end product. The user research should lead to functional and application requirements for both the application and the infrastructure. 

### Application:

A working application to support or execute one of the mission-critical activities of your client. This mission-critical activity can either be internal, so an activity that only involves employees, or external, involving both employees and third parties. At least three different users should make use of the application, each with their own user interface. On top of that, the application needs to process _a_ form of structured data into a database. This data can be generated by users, i.e. when they use the application, or stem from external sources, such as sensors or other IoT devices.

### Infrastructure:

A working infrastructure adhering to the functional and technical requirements set by the client. The infrastructure should be delivered as code, to allow rapid deployment of the infrastructure in new situations. The client expects full documentation of the infrastructure, including functional and technical tests.

The infrastructure should allow users from both inside and outside the company building to connect to the application. As this application is meant to support one of the primary processes, the client has requested appropriate cybersecurity countermeasures at the infrastructure level. They expect you to apply a hybrid approach to implementing these countermeasures: initially start with a baseline approach, and then move into a detailed risk analysis to identify any additional countermeasures that could be taken. 

### Infrastructure & Business Analysis

The client has requested an analysis of both the infrastructure and underlying business processes, to uncover any potential security risks. They would like you to investigate the following topics:

- The various business activities within the business unit. For each business activity, determine its importance to the organisation.
- The various IT assets within the business unit. For each IT asset, determine its importance to the organisation.
- Which cybersecurity standards and regulations are important for this business unit?
- What are the major cybersecurity threats that can impact the application and infrastructure?
- What are the major business and IT risks associated with the application and infrastructure?
- What countermeasures would mitigate these risks?

To ensure accuracy of the facts and figures within this report, the client has requested extensive sourcing: all major claims in your report should be supported by at least one trustworthy source. Fundamental claims in the report should be supported by at least two trustworthy sources. 

Furthermore, the client has requested that the analysis should cover both breadth and depth. The client wants a global analysis of all relevant processes and assets from multiple angles, to provide a breadth of perspectives. Each crown jewel should be analysed in-depth, to provide a thorough overview of the associated risk profile and potential mitigation strategies to protect the crown jewel.

