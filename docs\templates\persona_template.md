# Persona Template voor de Secure Infrastructure‑opdracht

In dit project is het belangrijk om jouw eindgebruikers (en hun behoeften) goed in kaart te brengen. Een **persona** is een hulpmiddel om de gebruiker te visualiseren en beter te begrijpen. Houd er rekening mee dat een persona altijd is gebaseerd op **echte data** uit bijvoorbeeld interviews, desk research, enquêtes of ander gebruikersonderzoek.

Onderstaande template helpt je bij het samenstellen van een persona. Vul de template in op basis van je desk research, gesprekken met potentiële gebruikers, of bestaande klant- en marktcijfers. Vergeet niet dat een persona een **typerepresentatie** van je doelgroep is, geen echte individuele persoon.

---

## 1. Algemene Informatie
**Naam persona**  
*(bijvoorbeeld: <PERSON>, Beveiligingsmanager bij Energiebedrijf)*

**Leeftijd en/of levensfase**  
*(bijvoorbeeld: 35 jaar, recent in dienst bij een groot bedrijf)*

**Functie en verantwoordelijkheden**  
*(bijvoorbeeld: verantwoordelijk voor het digitale toegangsbeheer en naleving van beveiligingsrichtlijnen)*

**Korte omschrijving van de persona**  
*(beschrijf de persona in één of twee zinnen: wie is deze persoon en wat is zijn/haar rol?)*

---

## 2. Achtergrond en Context
**Organisatie of werkveld**  
*(bijvoorbeeld: werkt bij een energiebedrijf dat kritieke infrastructuur beheert)*

**Relevante ervaring en/of kennis**  
*(welke expertise heeft deze persona al? Bijvoorbeeld: “beschikt over basiskennis van netwerkbeveiliging”)*

**Omgeving en middelen**  
*(met welke systemen of technologieën werkt de persona dagelijks? Bijvoorbeeld: “maakt veel gebruik van Linux-servers en VPN-toegang”)*

---

## 3. Doelen en Motivaties
1. **Belangrijkste doel(en)**  
   *(waarom gebruikt deze persona het product of de dienst? Wat hopen ze ermee te bereiken?)*
2. **Secundaire doelen**  
   *(wat zijn andere, misschien minder dringende doeleinden?)*

> **Tip:** Denk ook aan security-eisen. Een persona in een kritieke sector wil bijvoorbeeld downtime voorkomen of voldoen aan NIS2‑richtlijnen.

---

## 4. Pain Points (Pijnpunten)
- **Huidige problemen of frustraties**  
  *(bijvoorbeeld: “heeft geen goed overzicht van alle IoT-apparaten die op het netwerk zijn aangesloten”)*  
- **Belemmeringen of barrières**  
  *(waar loopt deze persona tegenaan bij het uitvoeren van zijn/haar werk? Welke tools ontbreken?)*

> **Voorbeeld**: “Claire heeft beperkte tijd om updates en patches te installeren op alle systemen, wat leidt tot veiligheidsrisico’s.”

---

## 5. Gebruikersscenario / Use Case
Beschrijf een kort scenario waarin deze persona met jouw product of oplossing in aanraking komt:
1. **Situatie**: *(bijvoorbeeld: Claire moet een nieuw netwerksegment opzetten voor IoT-sensoren)*
2. **Actie**: *(bijvoorbeeld: ze start de configuratie via de beheertool en scant het netwerk op apparaten)*
3. **Resultaat**: *(bijvoorbeeld: een centraal overzicht van alle aanwezige apparatuur en geautomatiseerd patch‑management)*

---

## 6. Technische Eisen en Voorkeuren
- **Apparaten en besturingssystemen**  
  *(bijvoorbeeld: Windows 11, Linux-servers, mobiele devices)*
- **Gewenste (beveiligings)features**  
  *(bijvoorbeeld: tweefactorauthenticatie, encryptie, audit logs)*
- **Integraties of koppelingen**  
  *(bijvoorbeeld: het systeem moet te koppelen zijn met SIEM-software, Active Directory, etc.)*

---

## 7. Persoonlijke Quote (optioneel)
Voeg een korte quote toe die de kern van de persona weergeeft:  
> “Zonder duidelijk overzicht en automatisering is het voor mij onmogelijk om elke server up-to-date te houden.”

---

## 8. Overige Details (optioneel)
- **Hobby’s, interesses, of andere relevante punten**  
  *(Dit kan helpen om de persona “menselijker” te maken, maar houd het beknopt en relevant.)*
- **Media- en communicatievoorkeuren**  
  *(Hoe krijgt deze gebruiker zijn/haar informatie? Bijv. via e-mail, dashboards, Slack, Teams, etc.)*

---

## Hoe gebruik je deze Persona?
- **Ontwerpbeslissingen**: Stem functionaliteiten en beveiligingsopties af op wat deze persona écht nodig heeft.  
- **Prioritering**: Focus op de uitdagingen (pain points) van deze persona, zodat je product of dienst een maximale meerwaarde biedt.  
- **Communicatie**: Houd deze persona in gedachten bij het ontwikkelen van documentatie, handleidingen en gebruikersinterfaces.

> **Belangrijk**: Werk je persona’s regelmatig bij. Gebruikersbehoeften kunnen veranderen, en nieuwe inzichten kunnen leiden tot aanpassingen in je persona’s.

---

## Meer Informatie en Inspiratie
Voor een uitgebreide Engelstalige uitleg over het opstellen van persona’s kun je terecht bij de [Mailchimp User Persona Guide](https://mailchimp.com/resources/how-to-create-a-user-persona-ux/?ds_c=DEPT_AOC_Google_Search_ROW_EN_NB_Acquire_Broad_DSA-Rsrc_T1&ds_kids=p80322579130&ds_a_lid=dsa-2227026702184&ds_cid=71700000119083203&ds_agid=58700008730253442&gad_source=1&gclid=CjwKCAiA-ty8BhA_EiwAkyoa3xF5xeg76bZh-Dpl9GdSc78MB0zM_YagP4oj4_cL-6ZI_ng6neSiPBoCpmsQAvD_BwE&gclsrc=aw.ds). Deze link is Engelstalig, maar biedt waardevolle inzichten in het maken en benutten van persona’s.

**Veel succes met het maken van jouw persona’s!**  
