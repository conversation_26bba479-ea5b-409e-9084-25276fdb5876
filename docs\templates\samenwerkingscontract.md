# Samenwerkingscontract (Template)

## Inleiding
In dit document leggen wij, als team, onze afspraken vast over hoe we samenwerken, communiceren en omgaan met verantwoordelijkheden en conflicten tijdens het **Secure Infrastructure**-project. Door deze afspraken schriftelijk te maken en gezamenlijk te ondertekenen, zorgen we voor een veilige en productieve werkomgeving waarin ieders stem wordt gehoord en gerespecteerd.

We gaan gedurende **6 sprints** aan de slag met het ontwerpen, realiseren en testen van een infrastructuur en applicatie in de context van Cyber Security. Dit samenwerkingscontract dient als leidraad en kan gaandeweg in overleg worden aangepast als daar unanieme behoefte aan is.

---

## 1. Teamgegevens
**Teamnaam:** …  
**Project:** Secure Infrastructure (Semester 2)

### **Teamleden**
1. **Naam:** …  
   **Persoonlijke leerdoelen** *(vul hier **minimaal twee** in, maar meer mag altijd!)*:  
   - …  
   - …  
   - *(optioneel)* …  
   **Superkracht:** …  

2. **Naam:** …  
   **Persoonlijke leerdoelen**:  
   - …  
   - …  
   - *(optioneel)* …  
   **Superkracht:** …  

3. **Naam:** …  
   **Persoonlijke leerdoelen**:  
   - …  
   - …  
   - *(optioneel)* …  
   **Superkracht:** …  

4. **Naam:** …  
   **Persoonlijke leerdoelen**:  
   - …  
   - …  
   - *(optioneel)* …  
   **Superkracht:** …  

*(Optioneel 5e teamlid)*  

---

## 2. Gezamenlijk Leerdoel
Naast individuele leerdoelen stellen we ook **gezamenlijke leerdoelen** vast. Bespreek als team één of twee gezamenlijke valkuilen die voor meerdere teamleden herkenbaar zijn, zoals:

- Timemanagement
- Uitstelgedrag
- Effectiever hulp vragen

Door gezamenlijk een leerdoel te formuleren, ondersteunen we elkaar in de groei en zorgen we ervoor dat we als team sterker worden. Dit kan ook een onderscheidend doel zijn, zoals excelleren in **Secure by Design** of **geavanceerde automatisering**. 

---

## 3. Doel van het Contract
- **Waarom dit contract?**  
  - We willen duidelijkheid over taken, communicatie, verantwoordelijkheden en werkwijze.  
  - We nemen beslissingen op een democratische manier, waarin ieders inbreng telt.  
  - We spreken af hoe we omgaan met conflicten, afwezigheid en feedback.

---

## 4. Teamrollen en Roulatie

### 4.1 Scrumrollen per Sprint
We gebruiken Scrum-rollen die elke **sprint** kunnen rouleren:

- **Scrum Master (SM)**: faciliteert het proces, organiseert stand-ups, sprint planning en retrospective.  
- **Product Owner (PO)**: bewaakt de backlog, stelt prioriteiten vast en beoordeelt de acceptatie van user stories.  
- **Development Team (Dev Team)**: (iedereen) voert de feitelijke taken uit (design, code, infrastructuur, testen).

**Roulatie-afspraak:**
- We willen dat **iedereen** minimaal één keer SM en één keer PO is geweest na 6 sprints.  

### 4.2 Specifieke Taken
Naast de Scrum-rollen verdelen we de volgende verantwoordelijkheden:

- **Documentatieverantwoordelijke**  
- **Code Reviewer**  
- **Security Check**  
- **Build & Deploy**  

*(Iedereen kan elkaar helpen, maar hier ligt de primaire verantwoordelijkheid.)*

---

## 5. Communicatie en Tools

### 5.1 Kanalen
Wij kiezen gezamenlijk één (of meerdere) van de volgende opties als ons hoofdcommunicatiemiddel:

- **Microsoft Teams**  
- **Discord**  
- **WhatsApp**  

### 5.2 Stand-ups
- **Dagelijkse Stand-up**: Kort overleg (max. 15 min) om bij te praten over voortgang, blokkades en planning.

### 5.3 Afwezigheid
- **Ziekte of overmacht**: meld dit zo snel mogelijk in de groepschat.  

---

## 6. Urenregistratie in het Team
We houden een urenstaat bij om inzicht te krijgen in de besteding van uren. Dit helpt om:

- De voortgang te bewaken.
- Efficiënter samen te werken.
- Problemen tijdig te signaleren.
- Een professionele werkhouding te ontwikkelen.

**Praktijk in bedrijven**  
In veel bedrijven wordt urenregistratie gebruikt om projecten te monitoren, kosten te bewaken en productiviteit te verbeteren. Ook kunnen teams zo onderling hun prestaties vergelijken en processen optimaliseren.

**Hoe werkt het binnen jouw project?**  
Elke maandag analyseren we de urenstaat en vergelijken deze met andere teams om te leren van elkaars werkwijze.

---

## 7. Verwachtingen en Gedrag
Om de samenwerking te optimaliseren en conflicten te voorkomen, hanteren we de volgende richtlijnen:

- **Ondersteuning**: We helpen teamleden bij hun ontwikkeling en dagen elkaar uit.  
- **Afspraken naleven**: Afspraak = afspraak. Wie zich niet aan afspraken houdt, bespreekt dit in de stand-up.  
- **Reflectie**: We plannen retrospectives om onze samenwerking continu te verbeteren.  
- **Escalatie**: Bij aanhoudende problemen bespreken we dit eerst binnen het team. Als dat niet werkt, schakelen we de docent in.  

---

## 8. Lencioni
Een goed samenwerkend team vereist vertrouwen en effectieve communicatie. Daarom verdiepen we ons in de **Piramide van Lencioni**:

[Meer informatie](https://agilewerken.nl/agile-en-scrum/piramide-van-lencioni/

**Opdracht:**  
1. Onderzoek en bespreek de vijf lagen van de Lencioni piramide.  
2. Zoek bronnen die het model beschrijven en neem deze op in het samenwerkingscontract.  
3. Bespreek en beschrijf kort wat iedere laag betekent voor jullie als team.  
4. Controleer of deze afspraken al in het contract staan en vul aan waar nodig.
5. TIP: Noteer welke Belbin teamrollen jullie per persoon invullen.  

---

## 9. Sancties
Bij structurele schending van afspraken kan het team passende maatregelen treffen. Deze worden in overleg vastgesteld en kunnen variëren afhankelijk van de ernst van de situatie. Voorbeelden van sancties kunnen onder andere zijn:

- **Eerste waarschuwing**: Een individueel gesprek binnen het team waarin de situatie wordt besproken en concrete verbeterafspraken worden gemaakt.
- **Tweede waarschuwing**: Een groepsgesprek waarin gezamenlijke afspraken worden herzien en herbevestigd.
- **Derde maatregel**: Indien de problemen aanhouden, wordt de docent ingeschakeld voor verdere bemiddeling en besluitvorming.

Elk teamlid heeft de verantwoordelijkheid om bij problemen tijdig het gesprek aan te gaan, zodat escalatie zoveel mogelijk wordt voorkomen.  

---

## 10. Ondertekening
Door dit contract te ondertekenen, verklaren wij ons te houden aan de afspraken en elkaar te ondersteunen.

| Naam teamlid  | Handtekening | Datum  |
|---------------|--------------|--------|
|               |              |        |
|               |              |        |
|               |              |        |
|               |              |        |
| *(optioneel)* |              |        |

*(Na ondertekening bewaren we dit document in de GitLab-repository.)*
