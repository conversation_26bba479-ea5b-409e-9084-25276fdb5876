# MySQL Workbench Setup for Leefstijl Centrum

This guide will help you set up MySQL Workbench connectivity for the Leefstijl Centrum application.

## 🚀 Quick Start

### Option 1: Docker Setup (Recommended)

1. **Start the MySQL environment:**
   ```bash
   docker-compose -f docker-compose-mysql.yml up -d
   ```

2. **Wait for services to start** (about 30-60 seconds)

3. **Access the application:**
   - Web App: http://localhost:5000
   - phpMyAdmin: http://localhost:8080
   - MySQL: localhost:3306

### Option 2: Local MySQL Installation

1. **Install MySQL Server** (if not already installed)
2. **Run the database setup script:**
   ```bash
   mysql -u root -p < database.sql
   ```
3. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```
4. **Run the MySQL-enabled app:**
   ```bash
   python app_mysql.py
   ```

## 🔧 MySQL Workbench Connection Setup

### Connection Parameters

| Parameter | Value |
|-----------|-------|
| **Connection Name** | Leefstijl Centrum |
| **Hostname** | localhost |
| **Port** | 3306 |
| **Username** | leefstijl_user |
| **Password** | leefstijl_password |
| **Default Schema** | leefstijl_centrum |

### Step-by-Step Connection Guide

1. **Open MySQL Workbench**

2. **Create New Connection:**
   - Click the "+" icon next to "MySQL Connections"
   - Or go to Database → Manage Connections → New

3. **Configure Connection:**
   ```
   Connection Name: Leefstijl Centrum
   Connection Method: Standard (TCP/IP)
   Hostname: localhost
   Port: 3306
   Username: leefstijl_user
   ```

4. **Set Password:**
   - Click "Store in Keychain..." (macOS) or "Store in Vault..." (Windows/Linux)
   - Enter password: `leefstijl_password`

5. **Test Connection:**
   - Click "Test Connection"
   - Should show "Successfully made the MySQL connection"

6. **Save & Connect:**
   - Click "OK" to save
   - Double-click the connection to connect

## 📊 Database Schema

### Tables Overview

#### `users` Table
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'therapist', 'client') NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### `appointments` Table
```sql
CREATE TABLE appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_name VARCHAR(100) NOT NULL,
    therapist VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    service VARCHAR(100) NOT NULL,
    status ENUM('Bevestigd', 'Wachtend', 'Geannuleerd', 'Voltooid') DEFAULT 'Wachtend',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🔍 Useful Queries for MySQL Workbench

### View All Users
```sql
SELECT * FROM users ORDER BY role, name;
```

### View All Appointments
```sql
SELECT * FROM appointments ORDER BY date, time;
```

### User Statistics
```sql
SELECT 
    role,
    COUNT(*) as user_count
FROM users 
GROUP BY role;
```

### Appointment Statistics
```sql
SELECT 
    status,
    COUNT(*) as appointment_count
FROM appointments 
GROUP BY status;
```

### Therapist Workload
```sql
SELECT 
    therapist,
    COUNT(*) as total_appointments,
    COUNT(CASE WHEN status = 'Bevestigd' THEN 1 END) as confirmed_appointments
FROM appointments 
GROUP BY therapist
ORDER BY total_appointments DESC;
```

### Upcoming Appointments
```sql
SELECT 
    client_name,
    therapist,
    date,
    time,
    service,
    status
FROM appointments 
WHERE date >= CURDATE()
ORDER BY date, time;
```

## 🛠️ Troubleshooting

### Connection Issues

**Problem:** "Can't connect to MySQL server"
**Solutions:**
1. Ensure MySQL service is running
2. Check if port 3306 is available
3. Verify firewall settings
4. For Docker: `docker-compose -f docker-compose-mysql.yml ps`

**Problem:** "Access denied for user"
**Solutions:**
1. Verify username: `leefstijl_user`
2. Verify password: `leefstijl_password`
3. Check user permissions in MySQL

### Docker Issues

**Problem:** Container won't start
**Solutions:**
1. Check Docker is running
2. Check port conflicts: `docker ps`
3. View logs: `docker-compose -f docker-compose-mysql.yml logs mysql`

**Problem:** Database not initialized
**Solutions:**
1. Remove volumes: `docker-compose -f docker-compose-mysql.yml down -v`
2. Restart: `docker-compose -f docker-compose-mysql.yml up -d`

## 📱 Application Access

### Demo Accounts
- **Admin**: `admin` / `admin123`
- **Therapist**: `fysio` / `fysio123`
- **Client**: `client2` / `client456`

### Health Check
Visit http://localhost:5000/health to verify database connectivity.

## 🔄 Data Migration

The application automatically migrates data from the existing CSV files to MySQL when you run the `database.sql` script. All existing users and appointments are preserved.

## 🎯 Next Steps

1. **Connect with MySQL Workbench** using the provided credentials
2. **Explore the database schema** and sample data
3. **Run queries** to understand the data structure
4. **Test the web application** with MySQL backend
5. **Monitor performance** using MySQL Workbench tools

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all services are running
3. Check application logs
4. Test database connectivity independently
