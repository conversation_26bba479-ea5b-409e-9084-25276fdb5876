# Leefstijl Centrum Mini

A minimal version of the Leefstijl Centrum web application with just the essential features.

## Features

- **Home Page**: Clean landing page with services overview
- **Login System**: Simple authentication with demo accounts
- **Agenda**: View appointments based on user role

## Demo Accounts

- **Admin**: `admin` / `admin123`
- **Therapist**: `fysio` / `fysio123`
- **Client**: `client` / `client123`

Additional accounts available in `data/users.csv`:
- **Therapist 2**: `medewerker` / `fysio123` (<PERSON><PERSON>)
- **Client 2**: `client2` / `client123` (<PERSON>)
- **Client 3**: `client3` / `client123` (<PERSON><PERSON>)

## Quick Start

1. Install Flask:
   ```bash
   pip install flask
   ```

2. Run the application:
   ```bash
   python app.py
   ```

3. Open your browser to: `http://localhost:5000`

## File Structure

```
leefstijl-centrum-mini/
├── app.py                 # Main Flask application
├── data/
│   ├── users.csv          # User accounts and credentials
│   └── appointments.csv   # Appointment data
├── static/
│   ├── css/
│   │   └── style.css      # All styles in one file
│   └── images/
│       └── LC-logo.png    # Leefstijl Centrum logo
├── templates/
│   ├── base.html          # Base template
│   ├── home.html          # Landing page
│   ├── login.html         # Login page
│   └── agenda.html        # Appointments page
└── README.md              # This file
```

## Data Storage

- **CSV Files**: User accounts and appointments stored in CSV format
- **Automatic Loading**: App reads from CSV files on startup
- **Fallback**: If CSV files are missing, uses in-memory sample data
- **Easy to Edit**: Modify CSV files directly to change data

## Design

- **Dropbox-inspired**: Clean, minimal, professional design
- **Responsive**: Works on desktop and mobile
- **Logo Integration**: LC logo in navigation, hero, and login
- **No dependencies**: Pure CSS, no frameworks
- **Fast**: Lightweight and optimized

## Development

This is a minimal prototype perfect for:
- Testing core functionality
- Rapid prototyping
- Learning Flask basics
- Design experimentation

Total lines of code: ~500 (vs 2000+ in full version)
