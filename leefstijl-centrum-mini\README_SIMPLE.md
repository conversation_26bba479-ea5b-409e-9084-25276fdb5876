# Simple MySQL Replacement

## Quick Setup (3 Steps)

### 1. Run the database setup script in MySQL Workbench:
```sql
-- Open and execute: database.sql
-- This creates the database, tables, and sample data
```

### 2. Install MySQL connector:
```bash
pip install mysql-connector-python
```

### 3. Run the MySQL-enabled app:
```bash
python app_mysql.py
```

## MySQL Workbench Connection:
- **Host:** localhost
- **Port:** 3306
- **Username:** leefstijl_user
- **Password:** leefstijl_password
- **Database:** leefstijl_centrum

## Test users:
- admin / admin123
- fysio / fysio123
- client2 / client456

## Docker Alternative:
```bash
docker-compose -f docker-compose-mysql.yml up -d
```

That's it! Your CSV data is now in MySQL with full Workbench connectivity.

📖 **For detailed setup instructions, see [MYSQL_SETUP.md](MYSQL_SETUP.md)**
