# MySQL Migration - CSV to Database

## 1. Setup Database
- Open `database_setup.sql` in MySQL Workbench
- Update file paths to match your system
- Click ⚡ Execute button

## 2. Configure App
- Open `app.py`
- Update MySQL username/password (lines 12-13)

## 3. Install & Run
```bash
pip install mysql-connector-python
python app.py
```

**Same app.py file - now powered by MySQL!**

📖 **See [WORKBENCH_SETUP.md](WORKBENCH_SETUP.md) for details**
