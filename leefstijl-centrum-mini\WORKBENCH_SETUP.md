# MySQL Migration - CSV to Database

This migrates your Flask app from CSV files to MySQL database for better performance with large datasets.

## 🚀 4-Step Migration

### Step 1: Setup Database
1. Open MySQL Workbench
2. Connect to your MySQL server
3. Open `database_setup.sql`
4. **Important**: Update the file paths in the script to match your system
5. Click ⚡ "Execute" button

### Step 2: Configure App
1. Open `app.py`
2. Update MySQL credentials (lines 12-13):
   ```python
   'user': 'your_mysql_username',
   'password': 'your_mysql_password',
   ```

### Step 3: Install Dependencies
```bash
pip install mysql-connector-python
```

### Step 4: Run App (Same as Before!)
```bash
python app.py
```

## ⚡ Why This is Better for Large Data

- **Efficient Import**: Uses MySQL's `LOAD DATA INFILE` - can handle millions of rows
- **Fast Queries**: Database indexing for quick searches
- **Scalable**: No memory limits like CSV files
- **Concurrent Access**: Multiple users can access data simultaneously

## 📊 What You Get

### Database: `leefstijl_centrum`
### Tables:
- **`users`** - All user accounts from users.csv
- **`appointments`** - All appointments from appointments.csv

## 🔍 Useful Queries to Try

### View all users by role:
```sql
SELECT role, COUNT(*) as count 
FROM users 
GROUP BY role;
```

### View upcoming appointments:
```sql
SELECT client_name, therapist, date, time, service 
FROM appointments 
WHERE status = 'Bevestigd'
ORDER BY date, time;
```

### Find all appointments for a specific therapist:
```sql
SELECT * FROM appointments 
WHERE therapist = 'yvette akkerman';
```

### View client information:
```sql
SELECT u.name, u.email, u.phone, 
       COUNT(a.id) as total_appointments
FROM users u
LEFT JOIN appointments a ON u.name = a.client_name
WHERE u.role = 'client'
GROUP BY u.username;
```

## 📈 Data Analysis Examples

### Therapist workload:
```sql
SELECT therapist, 
       COUNT(*) as total_appointments,
       COUNT(CASE WHEN status = 'Bevestigd' THEN 1 END) as confirmed
FROM appointments 
GROUP BY therapist;
```

### Service popularity:
```sql
SELECT service, COUNT(*) as bookings
FROM appointments 
GROUP BY service
ORDER BY bookings DESC;
```

## 🔄 Updating Data

When your CSV files change, just:
1. Update the INSERT statements in `database_setup.sql`
2. Run the script again in MySQL Workbench

## ✅ That's It!

Your Flask app continues to use CSV files, but now you also have the same data in MySQL Workbench for analysis, reporting, and database management.

**No code changes needed - your app keeps working exactly the same!**
