# MySQL Workbench Setup - Simple ETL Process

This creates a MySQL database from your CSV files. **No changes to your Flask app needed!**

## 🚀 3-Step Setup

### Step 1: Open MySQL Workbench
- Start MySQL Workbench
- Connect to your MySQL server (usually localhost)

### Step 2: Run the Database Script
1. Open the file `database_setup.sql` in MySQL Workbench
2. Click the ⚡ "Execute" button (or press Ctrl+Shift+Enter)
3. Wait for "Setup complete!" message

### Step 3: Explore Your Data
Your CSV data is now in MySQL! You can:
- Browse tables in the Navigator panel
- Run queries on your data
- View/edit data in the Result Grid

## 📊 What You Get

### Database: `leefstijl_centrum`
### Tables:
- **`users`** - All user accounts from users.csv
- **`appointments`** - All appointments from appointments.csv

## 🔍 Useful Queries to Try

### View all users by role:
```sql
SELECT role, COUNT(*) as count 
FROM users 
GROUP BY role;
```

### View upcoming appointments:
```sql
SELECT client_name, therapist, date, time, service 
FROM appointments 
WHERE status = 'Bevestigd'
ORDER BY date, time;
```

### Find all appointments for a specific therapist:
```sql
SELECT * FROM appointments 
WHERE therapist = 'yvette akkerman';
```

### View client information:
```sql
SELECT u.name, u.email, u.phone, 
       COUNT(a.id) as total_appointments
FROM users u
LEFT JOIN appointments a ON u.name = a.client_name
WHERE u.role = 'client'
GROUP BY u.username;
```

## 📈 Data Analysis Examples

### Therapist workload:
```sql
SELECT therapist, 
       COUNT(*) as total_appointments,
       COUNT(CASE WHEN status = 'Bevestigd' THEN 1 END) as confirmed
FROM appointments 
GROUP BY therapist;
```

### Service popularity:
```sql
SELECT service, COUNT(*) as bookings
FROM appointments 
GROUP BY service
ORDER BY bookings DESC;
```

## 🔄 Updating Data

When your CSV files change, just:
1. Update the INSERT statements in `database_setup.sql`
2. Run the script again in MySQL Workbench

## ✅ That's It!

Your Flask app continues to use CSV files, but now you also have the same data in MySQL Workbench for analysis, reporting, and database management.

**No code changes needed - your app keeps working exactly the same!**
