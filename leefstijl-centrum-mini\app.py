from flask import Flask, render_template, request, redirect, url_for, session, flash
from database import DataService
import random

# DataService is now imported from database.py

class AuthService:

    def __init__(self, data_service):
        self.data_service = data_service

    def authenticate_user(self, username, password):
        users = self.data_service.get_users()
        return username in users and users[username]['password'] == password

    def create_session(self, username):
        users = self.data_service.get_users()
        session['username'] = username
        session['name'] = users[username]['name']
        session['role'] = users[username]['role']

    def is_logged_in(self):
        return 'username' in session

    def clear_session(self):
        session.clear()

class AppointmentService:

    def __init__(self, data_service):
        self.data_service = data_service

    def filter_by_user(self, role, name):
        appointments = self.data_service.get_appointments()
        if role == 'client':
            return [apt for apt in appointments if apt['client_name'] == name]
        return appointments

app = Flask(__name__)
app.secret_key = 'Leefstijl-Centrum-Hoofddorp'

data_service = DataService()
auth_service = AuthService(data_service)
appointment_service = AppointmentService(data_service)

users = data_service.get_users()
appointments = data_service.get_appointments()





@app.route('/')
def home():
    return render_template('home.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        if auth_service.authenticate_user(username, password):
            auth_service.create_session(username)
            flash('Succesvol ingelogd!', 'success')
            return redirect(url_for('agenda'))
        else:
            flash('Ongeldige inloggegevens', 'error')

    return render_template('login.html')

@app.route('/agenda')
def agenda():
    if not auth_service.is_logged_in():
        flash('Je moet eerst inloggen', 'error')
        return redirect(url_for('login'))

    user_appointments = appointment_service.filter_by_user(
        session['role'],
        session['name']
    )

    return render_template('agenda.html', appointments=user_appointments)



@app.route('/logout')
def logout():
    auth_service.clear_session()
    flash('Je bent uitgelogd', 'info')
    return redirect(url_for('home'))

@app.route('/profile')
def profile():
    if not auth_service.is_logged_in():
        flash('Je moet eerst inloggen', 'error')
        return redirect(url_for('login'))

    # gebruikers stats
    user_appointments = appointment_service.filter_by_user(session['role'], session['name'])
    total_appointments = len(user_appointments)

    # random quotes (gemaakt door chat)
    quotes = [
        "Elke dag is een nieuwe kans om gezonder te worden!",
        "Jouw gezondheid is jouw rijkdom!",
        "Kleine stappen leiden tot grote veranderingen!",
        "Je bent sterker dan je denkt!",
        "Gezondheid is niet alles, maar zonder gezondheid is alles niets!"
    ]

    daily_quote = random.choice(quotes)

    return render_template('profile.html',
                         total_appointments=total_appointments,
                         daily_quote=daily_quote)

@app.route('/health')
def health_check():
    """Health check endpoint to verify database connection"""
    try:
        if data_service.test_connection():
            return {"status": "healthy", "database": "connected"}, 200
        else:
            return {"status": "unhealthy", "database": "disconnected"}, 500
    except Exception as e:
        return {"status": "error", "message": str(e)}, 500




if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)