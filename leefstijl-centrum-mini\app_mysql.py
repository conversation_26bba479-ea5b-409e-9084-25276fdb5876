from flask import Flask, render_template, request, redirect, url_for, session, flash
import mysql.connector
from mysql.connector import <PERSON>rror
import random
import os

class DatabaseConfig:
    """Database configuration class"""
    def __init__(self):
        self.config = {
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': os.getenv('DB_PORT', 3306),
            'database': os.getenv('DB_NAME', 'leefstijl_centrum'),
            'user': os.getenv('DB_USER', 'leefstijl_user'),
            'password': os.getenv('DB_PASSWORD', 'leefstijl_password'),
            'charset': 'utf8mb4',
            'collation': 'utf8mb4_unicode_ci',
            'autocommit': True
        }

class DataService:
    def __init__(self, db_config):
        self.db_config = db_config
        self.connection = None
        self.connect()

    def connect(self):
        """Establish database connection"""
        try:
            self.connection = mysql.connector.connect(**self.db_config.config)
            if self.connection.is_connected():
                print("Successfully connected to MySQL database")
        except Error as e:
            print(f"Error connecting to MySQL: {e}")
            self.connection = None

    def get_connection(self):
        """Get database connection, reconnect if needed"""
        if not self.connection or not self.connection.is_connected():
            self.connect()
        return self.connection

    def load_users(self):
        """Load users from MySQL database"""
        users = {}
        try:
            connection = self.get_connection()
            if connection:
                cursor = connection.cursor(dictionary=True)
                cursor.execute("SELECT * FROM users")
                rows = cursor.fetchall()
                
                for row in rows:
                    users[row['username']] = {
                        'id': row['id'],
                        'password': row['password'],
                        'name': row['name'],
                        'role': row['role'],
                        'email': row['email'],
                        'phone': row['phone']
                    }
                cursor.close()
        except Error as e:
            print(f"Error loading users: {e}")
        
        return users

    def load_appointments(self):
        """Load appointments from MySQL database"""
        appointments = []
        try:
            connection = self.get_connection()
            if connection:
                cursor = connection.cursor(dictionary=True)
                cursor.execute("SELECT * FROM appointments ORDER BY date, time")
                rows = cursor.fetchall()
                
                for row in rows:
                    appointments.append({
                        'id': row['id'],
                        'client_name': row['client_name'],
                        'therapist': row['therapist'],
                        'date': str(row['date']),
                        'time': str(row['time']),
                        'service': row['service'],
                        'status': row['status']
                    })
                cursor.close()
        except Error as e:
            print(f"Error loading appointments: {e}")
        
        return appointments

    def get_users(self):
        return self.load_users()

    def get_appointments(self):
        return self.load_appointments()

    def close_connection(self):
        """Close database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("MySQL connection closed")

class AuthService:
    def __init__(self, data_service):
        self.data_service = data_service

    def authenticate_user(self, username, password):
        users = self.data_service.get_users()
        return username in users and users[username]['password'] == password

    def create_session(self, username):
        users = self.data_service.get_users()
        session['username'] = username
        session['name'] = users[username]['name']
        session['role'] = users[username]['role']

    def is_logged_in(self):
        return 'username' in session

    def clear_session(self):
        session.clear()

class AppointmentService:
    def __init__(self, data_service):
        self.data_service = data_service

    def filter_by_user(self, role, name):
        appointments = self.data_service.get_appointments()
        if role == 'client':
            return [apt for apt in appointments if apt['client_name'] == name]
        return appointments

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'Leefstijl-Centrum-Hoofddorp'

# Initialize services
db_config = DatabaseConfig()
data_service = DataService(db_config)
auth_service = AuthService(data_service)
appointment_service = AppointmentService(data_service)

@app.route('/')
def home():
    return render_template('home.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        if auth_service.authenticate_user(username, password):
            auth_service.create_session(username)
            flash('Succesvol ingelogd!', 'success')
            return redirect(url_for('agenda'))
        else:
            flash('Ongeldige inloggegevens', 'error')

    return render_template('login.html')

@app.route('/agenda')
def agenda():
    if not auth_service.is_logged_in():
        flash('Je moet eerst inloggen', 'error')
        return redirect(url_for('login'))

    user_appointments = appointment_service.filter_by_user(
        session['role'],
        session['name']
    )

    return render_template('agenda.html', appointments=user_appointments)

@app.route('/logout')
def logout():
    auth_service.clear_session()
    flash('Je bent uitgelogd', 'info')
    return redirect(url_for('home'))

@app.route('/profile')
def profile():
    if not auth_service.is_logged_in():
        flash('Je moet eerst inloggen', 'error')
        return redirect(url_for('login'))

    # gebruikers stats
    user_appointments = appointment_service.filter_by_user(session['role'], session['name'])
    total_appointments = len(user_appointments)

    # random quotes (gemaakt door chat)
    quotes = [
        "Elke dag is een nieuwe kans om gezonder te worden!",
        "Jouw gezondheid is jouw rijkdom!",
        "Kleine stappen leiden tot grote veranderingen!",
        "Je bent sterker dan je denkt!",
        "Gezondheid is niet alles, maar zonder gezondheid is alles niets!"
    ]

    daily_quote = random.choice(quotes)

    return render_template('profile.html',
                         total_appointments=total_appointments,
                         daily_quote=daily_quote)

@app.route('/health')
def health_check():
    """Health check endpoint to verify database connection"""
    try:
        connection = data_service.get_connection()
        if connection and connection.is_connected():
            return {"status": "healthy", "database": "connected"}, 200
        else:
            return {"status": "unhealthy", "database": "disconnected"}, 500
    except Exception as e:
        return {"status": "error", "message": str(e)}, 500

# Cleanup on app shutdown
@app.teardown_appcontext
def close_db(error):
    if hasattr(data_service, 'close_connection'):
        data_service.close_connection()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
