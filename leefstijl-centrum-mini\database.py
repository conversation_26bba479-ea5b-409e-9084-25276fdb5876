"""
Database connection and data access layer for Leefstijl Centrum
"""

import mysql.connector
from mysql.connector import Error

class DatabaseConnection:
    """Handles MySQL database connections and queries"""
    
    def __init__(self):
        # MySQL connection configuration
        self.config = {
            'host': 'localhost',
            'database': 'leefstijl_centrum',
            'user': 'root',  # Change this to your MySQL username
            'password': 'your_password_here',  # Change this to your MySQL password
            'charset': 'utf8mb4'
        }

    def get_connection(self):
        """Get MySQL connection"""
        try:
            return mysql.connector.connect(**self.config)
        except Error as e:
            print(f"Database connection error: {e}")
            return None

    def execute_query(self, query, params=None):
        """Execute a SELECT query and return results"""
        connection = self.get_connection()
        if connection:
            try:
                cursor = connection.cursor(dictionary=True)
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                results = cursor.fetchall()
                return results
            except Error as e:
                print(f"Query execution error: {e}")
                return []
            finally:
                cursor.close()
                connection.close()
        return []

    def execute_update(self, query, params=None):
        """Execute an INSERT, UPDATE, or DELETE query"""
        connection = self.get_connection()
        if connection:
            try:
                cursor = connection.cursor()
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                connection.commit()
                return cursor.rowcount
            except Error as e:
                print(f"Update execution error: {e}")
                connection.rollback()
                return 0
            finally:
                cursor.close()
                connection.close()
        return 0

class DataService:
    """Data access service using DatabaseConnection"""
    
    def __init__(self):
        self.db = DatabaseConnection()

    def load_users(self):
        """Load users from MySQL database"""
        users = {}
        query = "SELECT * FROM users"
        rows = self.db.execute_query(query)
        
        for row in rows:
            users[row['username']] = {
                'password': row['password'],
                'name': row['name'],
                'role': row['role'],
                'email': row['email'],
                'phone': row['phone']
            }
        
        return users

    def load_appointments(self):
        """Load appointments from MySQL database"""
        appointments = []
        query = "SELECT * FROM appointments ORDER BY date, time"
        rows = self.db.execute_query(query)
        
        for row in rows:
            appointments.append({
                'id': row['id'],
                'client_name': row['client_name'],
                'therapist': row['therapist'],
                'date': row['date'],
                'time': row['time'],
                'service': row['service'],
                'status': row['status']
            })
        
        return appointments

    def get_users(self):
        """Get all users"""
        return self.load_users()

    def get_appointments(self):
        """Get all appointments"""
        return self.load_appointments()

    def get_user_by_username(self, username):
        """Get a specific user by username"""
        query = "SELECT * FROM users WHERE username = %s"
        rows = self.db.execute_query(query, (username,))
        
        if rows:
            row = rows[0]
            return {
                'username': row['username'],
                'password': row['password'],
                'name': row['name'],
                'role': row['role'],
                'email': row['email'],
                'phone': row['phone']
            }
        return None

    def get_appointments_by_client(self, client_name):
        """Get appointments for a specific client"""
        query = "SELECT * FROM appointments WHERE client_name = %s ORDER BY date, time"
        rows = self.db.execute_query(query, (client_name,))
        
        appointments = []
        for row in rows:
            appointments.append({
                'id': row['id'],
                'client_name': row['client_name'],
                'therapist': row['therapist'],
                'date': row['date'],
                'time': row['time'],
                'service': row['service'],
                'status': row['status']
            })
        
        return appointments

    def get_appointments_by_therapist(self, therapist_name):
        """Get appointments for a specific therapist"""
        query = "SELECT * FROM appointments WHERE therapist = %s ORDER BY date, time"
        rows = self.db.execute_query(query, (therapist_name,))
        
        appointments = []
        for row in rows:
            appointments.append({
                'id': row['id'],
                'client_name': row['client_name'],
                'therapist': row['therapist'],
                'date': row['date'],
                'time': row['time'],
                'service': row['service'],
                'status': row['status']
            })
        
        return appointments

    def test_connection(self):
        """Test database connection"""
        connection = self.db.get_connection()
        if connection:
            try:
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                return result is not None
            except Error as e:
                print(f"Connection test failed: {e}")
                return False
            finally:
                cursor.close()
                connection.close()
        return False
