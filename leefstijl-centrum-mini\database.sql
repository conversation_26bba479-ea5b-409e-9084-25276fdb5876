-- Leefstijl Centrum Database Schema
-- MySQL Workbench Compatible Script

-- Create database
CREATE DATABASE IF NOT EXISTS leefstijl_centrum;
USE leefstijl_centrum;

-- Drop tables if they exist (for clean setup)
DROP TABLE IF EXISTS appointments;
DROP TABLE IF EXISTS users;

-- Create users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'therapist', 'client') NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create appointments table
CREATE TABLE appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_name VARCHAR(100) NOT NULL,
    therapist VARCHAR(100) NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    service VARCHAR(100) NOT NULL,
    status ENUM('Bevestigd', 'Wachtend', 'Geannuleerd', 'Voltooid') DEFAULT 'Wachtend',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_client_name (client_name),
    INDEX idx_therapist (therapist),
    INDEX idx_date (date),
    INDEX idx_status (status)
);

-- Insert sample users (matching CSV data)
INSERT INTO users (username, password, name, role, email, phone) VALUES
('admin', 'admin123', 'Admin Beheerder', 'admin', '<EMAIL>', '06-12345678'),
('fysio', 'fysio123', 'yvette akkerman', 'therapist', '<EMAIL>', '06-87654321'),
('client', 'client123', 'karel', 'client', '<EMAIL>', '06-11223344'),
('medewerker', 'fysio123', 'mark', 'therapist', '<EMAIL>', '06-55667788'),
('client2', 'client456', 'chiel slot', 'client', '<EMAIL>', '06-99887766'),
('client3', 'client123', 'ali Nassiry', 'client', '<EMAIL>', '06-44556677');

-- Insert sample appointments (matching CSV data)
INSERT INTO appointments (id, client_name, therapist, date, time, service, status) VALUES
(1, 'chiel slot', 'yvette akkerman', '2025-01-15', '10:00:00', 'Fysiotherapie', 'Bevestigd'),
(2, 'Ali Nassiry', 'mark', '2025-01-15', '14:30:00', 'Personal Training', 'Bevestigd'),
(3, 'Nubia Hunting', 'mark', '2025-01-16', '09:00:00', 'Leefstijlcoaching', 'Wachtend'),
(4, 'Anthony ter Horst', 'yvette akkerman', '2025-01-16', '11:00:00', 'Fysiotherapie', 'Bevestigd'),
(5, 'Tim Loeffen', 'mark', '2025-01-17', '15:00:00', 'Personal Training', 'Bevestigd');

-- Create database user for the application
CREATE USER IF NOT EXISTS 'leefstijl_user'@'%' IDENTIFIED BY 'leefstijl_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON leefstijl_centrum.* TO 'leefstijl_user'@'%';
FLUSH PRIVILEGES;

-- Display created tables
SHOW TABLES;

-- Display sample data
SELECT 'Users Table:' as Info;
SELECT * FROM users;

SELECT 'Appointments Table:' as Info;
SELECT * FROM appointments;

-- Useful queries for development
SELECT 'User Roles Distribution:' as Info;
SELECT role, COUNT(*) as count FROM users GROUP BY role;

SELECT 'Appointments by Status:' as Info;
SELECT status, COUNT(*) as count FROM appointments GROUP BY status;

SELECT 'Appointments by Therapist:' as Info;
SELECT therapist, COUNT(*) as count FROM appointments GROUP BY therapist;
