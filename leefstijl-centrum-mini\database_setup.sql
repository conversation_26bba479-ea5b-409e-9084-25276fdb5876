-- Simple MySQL Database Setup for Leefstijl Centrum
-- Copy and paste this entire script into MySQL Workbench and run it

-- Create database
CREATE DATABASE IF NOT EXISTS leefstijl_centrum;
USE leefstijl_centrum;

-- Create users table (matches CSV structure exactly)
CREATE TABLE users (
    username VARCHA<PERSON>(50) PRIMARY KEY,
    password VARCHAR(255),
    name VARCHA<PERSON>(100),
    role VARCHAR(20),
    email VARCHAR(100),
    phone VARCHAR(20)
);

-- Create appointments table (matches CSV structure exactly)
CREATE TABLE appointments (
    id INT PRIMARY KEY,
    client_name VARCHAR(100),
    therapist VARCHAR(100),
    date VARCHAR(20),
    time VARCHAR(20),
    service VARCHAR(100),
    status VARCHAR(50)
);

-- Insert users data (copied directly from users.csv)
INSERT INTO users (username, password, name, role, email, phone) VALUES
('admin', 'admin123', 'Admin <PERSON>', 'admin', '<EMAIL>', '06-12345678'),
('fysio', 'fysio123', 'yvette a<PERSON>', 'therapist', '<EMAIL>', '06-87654321'),
('client', 'client123', 'karel', 'client', '<EMAIL>', '06-11223344'),
('medewerker', 'fysio123', 'mark', 'therapist', '<EMAIL>', '06-55667788'),
('client2', 'client456', 'chiel slot', 'client', '<EMAIL>', '06-99887766'),
('client3', 'client123', 'ali Nassiry', 'client', '<EMAIL>', '06-44556677');

-- Insert appointments data (copied directly from appointments.csv)
INSERT INTO appointments (id, client_name, therapist, date, time, service, status) VALUES
(1, 'chiel slot', 'yvette akkerman', '2025-01-15', '10:00', 'Fysiotherapie', 'Bevestigd'),
(2, 'Ali Nassiry', 'mark', '2025-01-15', '14:30', 'Personal Training', 'Bevestigd'),
(3, 'Nubia Hunting', 'mark', '2025-01-16', '09:00', 'Leefstijlcoaching', 'Wachtend'),
(4, 'Anthony ter Horst', 'yvette akkerman', '2025-01-16', '11:00', 'Fysiotherapie', 'Bevestigd'),
(5, 'Tim Loeffen', 'mark', '2025-01-17', '15:00', 'Personal Training', 'Bevestigd');

-- Show the results
SELECT 'Users imported:' as Status;
SELECT * FROM users;

SELECT 'Appointments imported:' as Status;
SELECT * FROM appointments;

SELECT 'Setup complete!' as Status;
