-- MySQL Database Setup for Leefstijl Centrum
-- Efficient CSV Import for Large Datasets

-- Create database
CREATE DATABASE IF NOT EXISTS leefstijl_centrum;
USE leefstijl_centrum;

-- Create users table
CREATE TABLE users (
    username VA<PERSON>HA<PERSON>(50) PRIMARY KEY,
    password VARCHAR(255),
    name VARCHAR(100),
    role VARCHAR(20),
    email VARCHAR(100),
    phone VARCHAR(20)
);

-- Create appointments table
CREATE TABLE appointments (
    id INT PRIMARY KEY,
    client_name VARCHAR(100),
    therapist VARCHAR(100),
    date VARCHAR(20),
    time VARCHAR(20),
    service VARCHAR(100),
    status VARCHAR(50)
);

-- Import users from CSV file
-- Replace 'C:/path/to/your/csv/files/' with your actual path
LOAD DATA INFILE 'C:/Users/<USER>/OneDrive/Bureaublad/school_ict2/qooduuleeluu25/leefstijl-centrum-mini/data/users.csv'
INTO TABLE users
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Import appointments from CSV file
LOAD DATA INFILE 'C:/Users/<USER>/OneDrive/Bureaublad/school_ict2/qooduuleeluu25/leefstijl-centrum-mini/data/appointments.csv'
INTO TABLE appointments
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS;

-- Show results
SELECT 'Import completed!' as Status;
SELECT CONCAT('Users imported: ', COUNT(*)) as Result FROM users;
SELECT CONCAT('Appointments imported: ', COUNT(*)) as Result FROM appointments;
