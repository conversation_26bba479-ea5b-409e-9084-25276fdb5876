version: '3.8'

services:
  # MySQL Database Service
  mysql:
    image: mysql:8.0
    container_name: leefstijl_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: leefstijl_centrum
      MYSQL_USER: leefstijl_user
      MYSQL_PASSWORD: leefstijl_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database.sql:/docker-entrypoint-initdb.d/database.sql
    networks:
      - leefstijl_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Flask Application Service
  leefstijl-centrum-mysql:
    build: .
    container_name: leefstijl_app_mysql
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=leefstijl_centrum
      - DB_USER=leefstijl_user
      - DB_PASSWORD=leefstijl_password
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - leefstijl_network
    command: python app_mysql.py

  # phpMyAdmin for database management (optional)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: leefstijl_phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: root_password
    ports:
      - "8080:80"
    depends_on:
      - mysql
    networks:
      - leefstijl_network

volumes:
  mysql_data:
    driver: local

networks:
  leefstijl_network:
    driver: bridge
