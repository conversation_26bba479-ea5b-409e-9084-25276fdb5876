# Leefstijl Centrum Mini - Procesflow Diagram

## Procesflow Diagram

```mermaid
flowchart TD
    A[Start: Gebruiker bezoekt website] --> B[Home Pagina]
    B --> C{Gebruiker ingelogd?}

    C -->|Nee| D[Login Pagina]
    C -->|Ja| E[Toon Navigatie Menu]

    D --> F[Voer Inloggegevens In]
    F --> G{Geldige gegevens?}
    G -->|Nee| H[Toon Foutmelding]
    H --> D
    G -->|Ja| I[Maak Sessie Aan]
    I --> J[Doorverwijzen naar Agenda]

    E --> K{Gebruiker selecteert}
    K -->|Home| B
    K -->|Profiel| L[Profiel Pagina]
    K -->|Agenda| M[Agenda Pagina]
    K -->|Uitloggen| N[Sessie Wissen]

    L --> O[Laad Gebruiker Statistieken]
    O --> P[Toon Dagelijkse Quote]
    P --> Q[Toon Profiel Informatie]
    Q --> K

    M --> R{Gebruiker Rol?}
    R -->|Cliënt| S[Filter: Alleen eigen afspraken]
    R -->|Admin/Therapeut| T[Toon: Alle afspraken]

    S --> U[Toon Afspraken Tabel]
    T --> U
    U --> V[Toon afspraak details:<br/>Datum, Tijd, Cliënt, Therapeut, Service, Status]
    V --> K

    N --> W[Doorverwijzen naar Home]
    W --> B

    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style L fill:#e8f5e8
    style M fill:#fff3e0
    style N fill:#ffebee
```

## Proces Beschrijving

### 🔄 **Hoofdgebruiker Flow:**
1. **Startpunt**: Gebruiker bezoekt de website
2. **Authenticatie Check**: Systeem controleert of gebruiker is ingelogd
3. **Login Proces**: Als niet ingelogd, moet gebruiker authenticeren
4. **Navigatie**: Eenmaal ingelogd kan gebruiker tussen pagina's navigeren
5. **Rol-Gebaseerde Toegang**: Verschillende gebruikers zien verschillende content op basis van hun rol

### 🎯 **Belangrijke Beslispunten:**
- **Authenticatie**: Geldige inloggegevens vereist voor toegang
- **Rol-Gebaseerde Filtering**: Cliënten zien alleen hun afspraken, personeel ziet alles
- **Sessie Beheer**: Veilig inlog/uitlog proces

### 📊 **Data Flow:**
- Gebruikersdata geladen uit CSV bestanden
- Afspraakdata gefilterd op gebruikersrol
- Sessie status behouden gedurende gebruikersreis
