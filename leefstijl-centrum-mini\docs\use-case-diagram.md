# Leefstijl Centrum Mini - Use Case Diagram

## Use Case Diagram

```mermaid
graph LR
    %% <PERSON>en links
    subgraph "👥 Gebruikers"
        Client[👤 Cliënt]
        Therapist[🩺 Therapeut]
        Admin[👨‍💼 Beheerder]
    end

    %% Publieke Use Cases (midden-links)
    subgraph "🌐 Publieke Functies"
        UC1[Bezoek Home Pagina]
        UC2[Inloggen in Systeem]
    end

    %% Geauthenticeerde Use Cases (midden)
    subgraph "🔐 Persoonlijke Functies"
        UC3[Bekijk Profiel]
        UC6[Uitloggen]
    end

    %% Agenda Use Cases (midden-rechts)
    subgraph "📅 Agenda Functies"
        UC4[Bekijk Persoonlijke Agenda]
        UC5[Bekijk Alle Afspraken]
    end

    %% Systeem Use Cases (rechts)
    subgraph "⚙️ Systeem Functies"
        UC7[Laad Gebruikersdata<br/>uit CSV]
        UC8[Laad Afspraakdata<br/>uit CSV]
        UC9[Authenticeer<br/>Gebruiker]
        UC10[<PERSON><PERSON><PERSON>]
        UC11[Filter <PERSON>ken<br/>op Rol]
    end

    %% Gebruiker connecties
    Client --> UC1
    Client --> UC2
    Client --> UC3
    Client --> UC4
    Client --> UC6

    Therapist --> UC1
    Therapist --> UC2
    Therapist --> UC3
    Therapist --> UC5
    Therapist --> UC6

    Admin --> UC1
    Admin --> UC2
    Admin --> UC3
    Admin --> UC5
    Admin --> UC6

    %% Systeem afhankelijkheden (gestippelde lijnen)
    UC2 -.-> UC9
    UC3 -.-> UC10
    UC4 -.-> UC7
    UC4 -.-> UC8
    UC4 -.-> UC11
    UC5 -.-> UC7
    UC5 -.-> UC8
    UC5 -.-> UC11

    %% Styling
    style Client fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    style Therapist fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    style Admin fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    style UC1 fill:#f3e5f5,stroke:#7b1fa2
    style UC2 fill:#f3e5f5,stroke:#7b1fa2
    style UC3 fill:#e8f5e8,stroke:#388e3c
    style UC6 fill:#e8f5e8,stroke:#388e3c
    style UC4 fill:#fff3e0,stroke:#f57c00
    style UC5 fill:#fff3e0,stroke:#f57c00
```

## Use Case Beschrijvingen

### 👤 **Cliënt Use Cases:**
- **Bezoek Home Pagina**: Toegang tot landingspagina met service informatie
- **Inloggen in Systeem**: Authenticeren met gebruikersnaam/wachtwoord
- **Bekijk Profiel**: Zie persoonlijke statistieken, afspraak aantal en dagelijkse motivatie
- **Bekijk Persoonlijke Agenda**: Zie alleen eigen afspraken in tabel formaat
- **Uitloggen**: Beëindig sessie en keer terug naar home pagina

### 🩺 **Therapeut Use Cases:**
- **Bezoek Home Pagina**: Toegang tot de landingspagina
- **Inloggen in Systeem**: Authenticeren met therapeut inloggegevens
- **Bekijk Profiel**: Zie persoonlijk dashboard met statistieken
- **Bekijk Alle Afspraken**: Zie alle afspraken van alle cliënten
- **Uitloggen**: Beëindig sessie veilig

### 👨‍💼 **Beheerder Use Cases:**
- **Bezoek Home Pagina**: Toegang tot de landingspagina
- **Inloggen in Systeem**: Authenticeren met beheerder inloggegevens
- **Bekijk Profiel**: Zie beheerder dashboard met systeem statistieken
- **Bekijk Alle Afspraken**: Zie compleet afspraken overzicht
- **Uitloggen**: Beëindig beheerder sessie

### ⚙️ **Systeem Use Cases:**
- **Laad Gebruikersdata uit CSV**: Lees gebruikersaccounts uit CSV bestand
- **Laad Afspraakdata uit CSV**: Lees afspraken uit CSV bestand
- **Authenticeer Gebruiker**: Valideer inloggegevens tegen gebruikersdatabase
- **Beheer Sessie**: Behandel gebruiker sessie status en beveiliging
- **Filter Afspraken op Rol**: Toon juiste afspraken gebaseerd op gebruikersrol
