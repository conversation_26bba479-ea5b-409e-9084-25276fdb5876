{% extends "base.html" %}

{% block title %}Agenda{% endblock %}

{% block content %}
<div class="container">
    <div class="agenda-header">
        <h2>Agenda</h2>
    </div>

    <div class="agenda-card">
        <div class="card-header">
            Appointments ({{ appointments|length }} total)
        </div>
        <div class="card-body">
            {% if appointments %}
                <div class="table-responsive">
                    <table class="agenda-table">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Time</th>
                                <th>Client</th>
                                <th>Therapist</th>
                                <th>Type</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for appointment in appointments %}
                            <tr>
                                <td>{{ appointment.date }}</td>
                                <td>{{ appointment.time }}</td>
                                <td>{{ appointment.client_name }}</td>
                                <td>{{ appointment.therapist }}</td>
                                <td>{{ appointment.service }}</td>
                                <td>
                                    {% if appointment.status == 'Bevestigd' %}
                                        <span class="status-confirmed">
                                            Confirmed
                                        </span>
                                    {% elif appointment.status == 'Wachtend' %}
                                        <span class="status-pending">
                                            Pending
                                        </span>
                                    {% else %}
                                        <span class="status-other">
                                            {{ appointment.status }}
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="empty-appointments">
                    <h5>No appointments found</h5>
                    <p>There are currently no appointments scheduled.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
