<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Leefstijl Centrum{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <div class="nav-content">
                <a href="{{ url_for('home') }}" class="nav-brand">
                    <img src="{{ url_for('static', filename='images/LC-logo.png') }}"
                         alt="LC"
                         style="height: 32px; margin-right: 12px; vertical-align: middle;">
                    Leefstijl Centrum
                </a>
                <ul class="nav-links">
                    <li><a href="{{ url_for('home') }}" class="nav-link">Home</a></li>
                    {% if session.username %}
                        <li><a href="{{ url_for('profile') }}" class="nav-link">Profiel</a></li>
                        <li><a href="{{ url_for('agenda') }}" class="nav-link">Agenda</a></li>
                        <li><a href="{{ url_for('logout') }}" class="nav-link">Uitloggen</a></li>
                    {% else %}
                        <li><a href="{{ url_for('login') }}" class="nav-link">Inloggen</a></li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container">
                <div class="flash-messages">
                    {% for category, message in messages %}
                        <div class="flash flash-{{ category }}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% endif %}
    {% endwith %}

    <main>
        {% block content %}{% endblock %}
    </main>

    <script>
        // snelle login function
        function quickLogin(username, password) {
            document.querySelector('input[name="username"]').value = username;
            document.querySelector('input[name="password"]').value = password;
        }
    </script>
</body>
</html>
