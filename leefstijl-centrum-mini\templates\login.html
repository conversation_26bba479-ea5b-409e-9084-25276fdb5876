{% extends "base.html" %}

{% block title %}Inloggen - Leefstijl Centrum{% endblock %}

{% block content %}
<div class="login-container">
    <div class="container">
        <div class="login-card">
            
            <div class="login-welcome">
                <h1>Yo</h1>
                <p>
                    Log in om toegang te krijgen tot jouw agenda en alle functies
                    van het Leefstijl Centrum.
                </p>
                <div style="margin-top: 40px;">
                    <img src="{{ url_for('static', filename='images/LC-logo.png') }}"
                         alt="LC Logo"
                         style="max-width: 80px; opacity: 0.8;">
                </div>
            </div>
            
            <div class="login-form">
                <h2>Inloggen</h2>
                <p>Voer je gegevens in om door te gaan</p>
                
                <form method="POST">
                    <div class="form-group">
                        <label class="form-label">Gebruikersnaam</label>
                        <input type="text" name="username" class="form-input" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Wachtwoord</label>
                        <input type="text" name="password" class="form-input" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 20px;">
                        Inloggen
                    </button>
                </form>
                
                <p style="font-size: 14px; color: #5c5856; margin-bottom: 12px;">
                    Demo accounts:
                </p>
                <div class="demo-buttons">
                    <button onclick="quickLogin('admin', 'admin123')" class="demo-btn">
                        Admin
                    </button>
                    <button onclick="quickLogin('fysio', 'fysio123')" class="demo-btn">
                        Fysio
                    </button>
                    <button onclick="quickLogin('client2', 'client456')" class="demo-btn">
                        Cliënt
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
