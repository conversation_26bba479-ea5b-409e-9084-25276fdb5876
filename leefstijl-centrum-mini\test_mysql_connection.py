#!/usr/bin/env python3
"""
MySQL Connection Test Script for Leefstijl Centrum
This script tests the database connection and displays sample data.
"""

import mysql.connector
from mysql.connector import Error
import os

def test_connection():
    """Test MySQL database connection"""
    
    # Database configuration
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', 3306),
        'database': os.getenv('DB_NAME', 'leefstijl_centrum'),
        'user': os.getenv('DB_USER', 'leefstijl_user'),
        'password': os.getenv('DB_PASSWORD', 'leefstijl_password'),
        'charset': 'utf8mb4',
        'collation': 'utf8mb4_unicode_ci'
    }
    
    print("🔍 Testing MySQL Connection...")
    print(f"Host: {config['host']}:{config['port']}")
    print(f"Database: {config['database']}")
    print(f"User: {config['user']}")
    print("-" * 50)
    
    try:
        # Establish connection
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ Successfully connected to MySQL database!")
            
            # Get database info
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"📊 MySQL Server version: {version[0]}")
            
            # Test users table
            print("\n👥 Users Table:")
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            print(f"   Total users: {user_count}")
            
            cursor.execute("SELECT role, COUNT(*) FROM users GROUP BY role")
            roles = cursor.fetchall()
            for role, count in roles:
                print(f"   - {role}: {count}")
            
            # Test appointments table
            print("\n📅 Appointments Table:")
            cursor.execute("SELECT COUNT(*) FROM appointments")
            appointment_count = cursor.fetchone()[0]
            print(f"   Total appointments: {appointment_count}")
            
            cursor.execute("SELECT status, COUNT(*) FROM appointments GROUP BY status")
            statuses = cursor.fetchall()
            for status, count in statuses:
                print(f"   - {status}: {count}")
            
            # Sample data
            print("\n📋 Sample Users:")
            cursor.execute("SELECT username, name, role FROM users LIMIT 3")
            users = cursor.fetchall()
            for username, name, role in users:
                print(f"   - {username}: {name} ({role})")
            
            print("\n📋 Sample Appointments:")
            cursor.execute("SELECT client_name, therapist, date, service FROM appointments LIMIT 3")
            appointments = cursor.fetchall()
            for client, therapist, date, service in appointments:
                print(f"   - {client} → {therapist} on {date} ({service})")
            
            cursor.close()
            print("\n🎉 Database connection test completed successfully!")
            
    except Error as e:
        print(f"❌ Error connecting to MySQL: {e}")
        print("\n🔧 Troubleshooting tips:")
        print("1. Make sure MySQL server is running")
        print("2. Verify connection parameters")
        print("3. Check if database and user exist")
        print("4. For Docker: docker-compose -f docker-compose-mysql.yml up -d")
        
    finally:
        if 'connection' in locals() and connection.is_connected():
            connection.close()
            print("🔌 MySQL connection closed.")

if __name__ == "__main__":
    test_connection()
